#!/bin/bash

# Enhanced Log Monitoring Script for Pharmacy Backend
# Usage: ./scripts/logs.sh [command] [options]

LOG_DIR="./logs"
TODAY=$(date +%Y-%m-%d)
TODAY_LOG="$LOG_DIR/pharmacy-backend-$TODAY.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    echo -e "${1}${2}${NC}"
}

# Function to check if log directory exists
check_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        print_colored $RED "❌ Log directory tidak ditemukan: $LOG_DIR"
        exit 1
    fi
}

# Function to get latest log file
get_latest_log() {
    if [ -f "$TODAY_LOG" ]; then
        echo "$TODAY_LOG"
    else
        # Include rotated log files (.log.1, .log.2, etc.)
        ls -t $LOG_DIR/*.log* 2>/dev/null | head -1
    fi
}

# Function to show help
show_help() {
    print_colored $CYAN "🚀 Pharmacy Backend Log Monitor"
    echo "================================"
    echo ""
    print_colored $GREEN "📖 Available Commands:"
    echo ""
    print_colored $YELLOW "🔍 Real-time Monitoring:"
    echo "  ./scripts/logs.sh watch          - Monitor today's log file"
    echo "  ./scripts/logs.sh watch-all      - Monitor all log files"
    echo "  ./scripts/logs.sh dev            - Development-friendly monitoring"
    echo ""
    print_colored $YELLOW "📊 Filtering:"
    echo "  ./scripts/logs.sh errors         - Show only errors (level >= 40)"
    echo "  ./scripts/logs.sh warnings       - Show warnings and above (level >= 30)"
    echo "  ./scripts/logs.sh context <name> - Filter by context (e.g., AuthService)"
    echo "  ./scripts/logs.sh search <term>  - Search for specific term"
    echo ""
    print_colored $YELLOW "📁 File Operations:"
    echo "  ./scripts/logs.sh pretty         - Pretty print today's log"
    echo "  ./scripts/logs.sh latest         - Pretty print latest log file"
    echo "  ./scripts/logs.sh list           - List all log files"
    echo "  ./scripts/logs.sh clean          - Clean old log files (>7 days)"
    echo ""
    print_colored $YELLOW "🎯 Advanced:"
    echo "  ./scripts/logs.sh tail <file>    - Monitor specific log file"
    echo "  ./scripts/logs.sh stats          - Show log statistics"
    echo ""
    print_colored $BLUE "📋 Log Levels:"
    echo "  10 = trace (development debugging)"
    echo "  20 = debug (detailed application state)"
    echo "  30 = info  (general information)"
    echo "  40 = warn  (potential issues)"
    echo "  50 = error (operation failures)"
    echo "  60 = fatal (unrecoverable errors)"
    echo ""
    print_colored $PURPLE "💡 Tips:"
    echo "  - Press Ctrl+C to stop monitoring"
    echo "  - Use 'bun run logs:*' commands from package.json"
    echo "  - Log files are auto-rotated daily"
}

# Function to list log files
list_logs() {
    check_log_dir
    print_colored $CYAN "📁 Available log files:"
    echo ""
    
    # Check for both .log and .log.* files (rotated logs)
    log_files=$(ls $LOG_DIR/*.log* 2>/dev/null)
    if [ -z "$log_files" ]; then
        print_colored $RED "❌ No log files found"
        return
    fi

    ls -la $LOG_DIR/*.log* | while read -r line; do
        filename=$(echo "$line" | awk '{print $9}')
        size=$(echo "$line" | awk '{print $5}')
        date=$(echo "$line" | awk '{print $6, $7, $8}')
        basename=$(basename "$filename")
        
        # Convert size to human readable
        if [ "$size" -gt 1048576 ]; then
            size_human=$(echo "scale=2; $size/1048576" | bc -l 2>/dev/null || echo "$size")
            size_unit="MB"
        elif [ "$size" -gt 1024 ]; then
            size_human=$(echo "scale=2; $size/1024" | bc -l 2>/dev/null || echo "$size")
            size_unit="KB"
        else
            size_human="$size"
            size_unit="B"
        fi
        
        echo "  📄 $basename (${size_human}${size_unit}, $date)"
    done
}

# Function to show log statistics
show_stats() {
    check_log_dir
    local log_file=$(get_latest_log)
    
    if [ -z "$log_file" ] || [ ! -f "$log_file" ]; then
        print_colored $RED "❌ No log file found"
        return
    fi
    
    print_colored $CYAN "📊 Log Statistics for $(basename $log_file):"
    echo ""
    
    # Count by log level
    print_colored $GREEN "📈 Log Levels:"
    echo "  🔍 Trace:  $(grep '"level":10' "$log_file" | wc -l | tr -d ' ')"
    echo "  🐛 Debug:  $(grep '"level":20' "$log_file" | wc -l | tr -d ' ')"
    echo "  ℹ️  Info:   $(grep '"level":30' "$log_file" | wc -l | tr -d ' ')"
    echo "  ⚠️  Warn:   $(grep '"level":40' "$log_file" | wc -l | tr -d ' ')"
    echo "  ❌ Error:  $(grep '"level":50' "$log_file" | wc -l | tr -d ' ')"
    echo "  💀 Fatal:  $(grep '"level":60' "$log_file" | wc -l | tr -d ' ')"
    echo ""
    
    # Total lines
    local total_lines=$(wc -l < "$log_file")
    print_colored $GREEN "📝 Total log entries: $total_lines"
    
    # File size
    local file_size=$(ls -lh "$log_file" | awk '{print $5}')
    print_colored $GREEN "💾 File size: $file_size"
    
    # Most active contexts
    print_colored $GREEN "🎯 Top 5 Active Contexts:"
    grep -o '"context":"[^"]*"' "$log_file" | sort | uniq -c | sort -nr | head -5 | while read count context; do
        context_name=$(echo "$context" | sed 's/"context":"//g' | sed 's/"//g')
        echo "  📦 $context_name: $count entries"
    done
}

# Function to get base pino-pretty arguments with level mapping
get_base_pino_args() {
    echo "--levelFirst --colorize --translateTime --ignore pid,hostname -x trace:10,debug:20,info:30,warn:40,error:50,fatal:60"
}

# Function to monitor logs with pino-pretty
monitor_logs() {
    local log_file="$1"
    local extra_args="$2"

    if [ -z "$log_file" ]; then
        log_file=$(get_latest_log)
    fi

    if [ -z "$log_file" ] || [ ! -f "$log_file" ]; then
        print_colored $RED "❌ Log file tidak ditemukan: $log_file"
        return
    fi

    print_colored $GREEN "🔍 Monitoring: $(basename $log_file)"
    print_colored $BLUE "📁 Path: $log_file"
    print_colored $YELLOW "⌨️  Press Ctrl+C to stop"
    echo ""

    # Combine base args with extra args
    local base_args=$(get_base_pino_args)
    local full_args="$base_args $extra_args"

    # Use tail -f and pipe to pino-pretty
    tail -f "$log_file" | bun x pino-pretty $full_args
}

# Main command handling
case "$1" in
    "watch")
        monitor_logs "$TODAY_LOG" ""
        ;;
    "watch-all")
        monitor_logs "" ""
        ;;
    "dev")
        monitor_logs "$TODAY_LOG" "--ignore service,environment"
        ;;
    "errors")
        monitor_logs "$TODAY_LOG" "-L warn"
        ;;
    "warnings")
        monitor_logs "$TODAY_LOG" "-L info"
        ;;
    "context")
        if [ -z "$2" ]; then
            print_colored $RED "❌ Context name required. Usage: ./scripts/logs.sh context <name>"
            exit 1
        fi
        # Use grep to filter by context since pino-pretty doesn't have search
        log_file=$(get_latest_log)
        if [ -f "$log_file" ]; then
            print_colored $GREEN "🔍 Monitoring context '$2': $(basename $log_file)"
            print_colored $BLUE "📁 Path: $log_file"
            print_colored $YELLOW "⌨️  Press Ctrl+C to stop"
            echo ""
            local base_args=$(get_base_pino_args)
            tail -f "$log_file" | grep "\"context\":\"$2\"" | bun x pino-pretty $base_args
        else
            print_colored $RED "❌ No log file found"
        fi
        ;;
    "search")
        if [ -z "$2" ]; then
            print_colored $RED "❌ Search term required. Usage: ./scripts/logs.sh search <term>"
            exit 1
        fi
        # Use grep to filter by search term
        log_file=$(get_latest_log)
        if [ -f "$log_file" ]; then
            print_colored $GREEN "🔍 Searching for '$2': $(basename $log_file)"
            print_colored $BLUE "📁 Path: $log_file"
            print_colored $YELLOW "⌨️  Press Ctrl+C to stop"
            echo ""
            local base_args=$(get_base_pino_args)
            tail -f "$log_file" | grep -i "$2" | bun x pino-pretty $base_args
        else
            print_colored $RED "❌ No log file found"
        fi
        ;;
    "pretty")
        log_file=$(get_latest_log)
        if [ -f "$log_file" ]; then
            print_colored $GREEN "🎨 Pretty printing: $(basename $log_file)"
            local base_args=$(get_base_pino_args)
            bun x pino-pretty $base_args < "$log_file"
        else
            print_colored $RED "❌ No log file found"
        fi
        ;;
    "latest")
        log_file=$(get_latest_log)
        if [ -f "$log_file" ]; then
            print_colored $GREEN "🎨 Pretty printing latest: $(basename $log_file)"
            local base_args=$(get_base_pino_args)
            bun x pino-pretty $base_args < "$log_file"
        else
            print_colored $RED "❌ No log file found"
        fi
        ;;
    "tail")
        if [ -z "$2" ]; then
            print_colored $RED "❌ File name required. Usage: ./scripts/logs.sh tail <filename>"
            exit 1
        fi
        monitor_logs "$LOG_DIR/$2" ""
        ;;
    "list")
        list_logs
        ;;
    "stats")
        show_stats
        ;;
    "clean")
        check_log_dir
        print_colored $YELLOW "🧹 Cleaning log files older than 7 days..."
        deleted=$(find "$LOG_DIR" -name "*.log" -mtime +7 -delete -print | wc -l | tr -d ' ')
        print_colored $GREEN "✅ Deleted $deleted old log files"
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        print_colored $RED "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
