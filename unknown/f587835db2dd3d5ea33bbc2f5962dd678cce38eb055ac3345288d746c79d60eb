#!/usr/bin/env node

/**
 * Enhanced Log Monitor for Pharmacy Backend
 * 
 * This script provides an interactive log monitoring experience with:
 * - Real-time log streaming with pino-pretty formatting
 * - Log level filtering
 * - Context-based filtering
 * - Search functionality
 * - Multiple log file support
 * - Color-coded output
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class LogMonitor {
  constructor() {
    this.logDir = path.join(__dirname, '..', 'logs');
    this.currentProcess = null;
    this.isRunning = false;
    this.filters = {
      level: null,
      context: null,
      search: null
    };
  }

  /**
   * Get available log files
   */
  getLogFiles() {
    try {
      if (!fs.existsSync(this.logDir)) {
        console.log('❌ Log directory tidak ditemukan:', this.logDir);
        return [];
      }

      const files = fs.readdirSync(this.logDir)
        .filter(file => file.endsWith('.log'))
        .map(file => {
          const fullPath = path.join(this.logDir, file);
          const stats = fs.statSync(fullPath);
          return {
            name: file,
            path: fullPath,
            size: stats.size,
            modified: stats.mtime
          };
        })
        .sort((a, b) => b.modified - a.modified);

      return files;
    } catch (error) {
      console.error('❌ Error reading log files:', error.message);
      return [];
    }
  }

  /**
   * Get today's log file
   */
  getTodayLogFile() {
    const today = new Date().toISOString().split('T')[0];
    const todayFile = `pharmacy-backend-${today}.log`;
    const todayPath = path.join(this.logDir, todayFile);
    
    if (fs.existsSync(todayPath)) {
      return todayPath;
    }

    // If today's file doesn't exist, get the latest log file
    const files = this.getLogFiles();
    return files.length > 0 ? files[0].path : null;
  }

  /**
   * Build pino-pretty command with filters
   */
  buildPinoPrettyCommand() {
    const args = [
      '--levelFirst',
      '--colorize',
      '--translateTime',
      '--ignore', 'pid,hostname',
      '-x', 'trace:10,debug:20,info:30,warn:40,error:50,fatal:60'
    ];

    // Add level filter
    if (this.filters.level) {
      args.push('--search', `level>=${this.filters.level}`);
    }

    // Add context filter
    if (this.filters.context) {
      args.push('--search', `context~${this.filters.context}`);
    }

    // Add search filter
    if (this.filters.search) {
      args.push('--search', this.filters.search);
    }

    return args;
  }

  /**
   * Start monitoring logs
   */
  startMonitoring(logFile = null) {
    const targetFile = logFile || this.getTodayLogFile();
    
    if (!targetFile) {
      console.log('❌ Tidak ada file log yang ditemukan');
      return;
    }

    if (!fs.existsSync(targetFile)) {
      console.log('❌ File log tidak ditemukan:', targetFile);
      return;
    }

    console.log('🔍 Monitoring log file:', path.basename(targetFile));
    console.log('📁 Path:', targetFile);
    
    if (Object.values(this.filters).some(f => f !== null)) {
      console.log('🔧 Active filters:', this.filters);
    }
    
    console.log('⌨️  Press Ctrl+C to stop, or type commands (help for options)\n');

    // Kill existing process
    this.stopMonitoring();

    // Start tail process
    const tailProcess = spawn('tail', ['-f', targetFile]);
    
    // Start pino-pretty process
    const pinoPrettyArgs = this.buildPinoPrettyCommand();
    const pinoProcess = spawn('pino-pretty', pinoPrettyArgs);

    // Pipe tail output to pino-pretty
    tailProcess.stdout.pipe(pinoProcess.stdin);

    // Handle pino-pretty output
    pinoProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });

    pinoProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });

    // Handle process errors
    tailProcess.on('error', (error) => {
      console.error('❌ Tail process error:', error.message);
    });

    pinoProcess.on('error', (error) => {
      console.error('❌ Pino-pretty process error:', error.message);
    });

    // Store processes for cleanup
    this.currentProcess = {
      tail: tailProcess,
      pino: pinoProcess
    };
    this.isRunning = true;

    // Handle process exit
    const cleanup = () => {
      this.stopMonitoring();
      process.exit(0);
    };

    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.currentProcess) {
      try {
        this.currentProcess.tail.kill();
        this.currentProcess.pino.kill();
      } catch (error) {
        // Ignore errors when killing processes
      }
      this.currentProcess = null;
    }
    this.isRunning = false;
  }

  /**
   * Show help
   */
  showHelp() {
    console.log(`
📖 Log Monitor Commands:

🔍 Monitoring:
  bun run logs:watch           - Monitor today's log file
  bun run logs:watch:all       - Monitor all log files
  bun run logs:dev             - Monitor with development-friendly format

📊 Filtering:
  bun run logs:filter:error    - Show only errors (level >= 40)
  bun run logs:filter:warn     - Show warnings and above (level >= 30)
  
📁 File Operations:
  bun run logs:pretty          - Pretty print today's log file
  bun run logs:pretty:latest   - Pretty print latest log file
  bun run logs:clean           - Clean old log files (>7 days)

🎯 Advanced Usage:
  node scripts/log-monitor.js --level=40 --context=AuthService
  node scripts/log-monitor.js --search="error" --file=specific-file.log

📋 Log Levels:
  10 = trace (development debugging)
  20 = debug (detailed application state)
  30 = info  (general information)
  40 = warn  (potential issues)
  50 = error (operation failures)
  60 = fatal (unrecoverable errors)
`);
  }

  /**
   * Interactive mode
   */
  async startInteractive() {
    console.log('🚀 Pharmacy Backend Log Monitor');
    console.log('================================\n');

    const files = this.getLogFiles();
    if (files.length === 0) {
      console.log('❌ Tidak ada file log yang ditemukan');
      return;
    }

    console.log('📁 Available log files:');
    files.forEach((file, index) => {
      const size = (file.size / 1024).toFixed(2);
      const modified = file.modified.toLocaleString('id-ID');
      console.log(`  ${index + 1}. ${file.name} (${size} KB, ${modified})`);
    });

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('\n🔍 Select file number (Enter for latest): ', (answer) => {
      const fileIndex = answer.trim() ? parseInt(answer) - 1 : 0;
      const selectedFile = files[fileIndex];

      if (!selectedFile) {
        console.log('❌ Invalid file selection');
        rl.close();
        return;
      }

      rl.close();
      this.startMonitoring(selectedFile.path);
    });
  }
}

// CLI handling
const args = process.argv.slice(2);
const monitor = new LogMonitor();

// Parse command line arguments
const options = {};
args.forEach(arg => {
  if (arg.startsWith('--level=')) {
    options.level = parseInt(arg.split('=')[1]);
  } else if (arg.startsWith('--context=')) {
    options.context = arg.split('=')[1];
  } else if (arg.startsWith('--search=')) {
    options.search = arg.split('=')[1];
  } else if (arg.startsWith('--file=')) {
    options.file = arg.split('=')[1];
  } else if (arg === '--help' || arg === '-h') {
    monitor.showHelp();
    process.exit(0);
  }
});

// Apply filters
if (options.level) monitor.filters.level = options.level;
if (options.context) monitor.filters.context = options.context;
if (options.search) monitor.filters.search = options.search;

// Start monitoring
if (options.file) {
  const filePath = path.join(monitor.logDir, options.file);
  monitor.startMonitoring(filePath);
} else if (args.length === 0) {
  monitor.startInteractive();
} else {
  monitor.startMonitoring();
}
